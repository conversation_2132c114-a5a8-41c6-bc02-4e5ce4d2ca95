<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 优眠医学中心</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/login.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>优眠医学中心</h1>
            <p>用户登录</p>
        </header>

        <main class="main">
            <div class="login-form">
                <div class="login-header">
                    <h2 id="loginTitle">登录</h2>
                    <p id="loginSubtitle">请输入您的登录信息</p>
                </div>

                <form id="loginForm">
                    <!-- 诊室选择（仅医助显示） -->
                    <div class="form-group" id="clinicGroup" style="display: none;">
                        <label for="clinicId">选择诊室</label>
                        <select id="clinicId" name="clinicId" required>
                            <option value="">请选择诊室</option>
                        </select>
                    </div>

                    <!-- 密码输入 -->
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required>
                    </div>

                    <button type="submit" class="btn btn-primary" id="loginBtn">
                        登录
                    </button>
                </form>

                <div class="login-footer">
                    <a href="index.html" class="btn btn-secondary">返回首页</a>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 优眠医学中心. All rights reserved.</p>
        </footer>
    </div>

    <script src="scripts/common.js"></script>
    <script>
        let currentRole = '';

        console.log('Login page script loaded');

        // 获取URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            
            // 获取角色参数
            currentRole = getUrlParam('role');
            console.log('Role from URL:', currentRole);
            
            if (!currentRole) {
                console.log('No role specified, redirecting');
                window.location.href = 'index.html';
                return;
            }

            // 初始化页面
            initializePage();
            
            // 绑定表单事件
            const form = document.getElementById('loginForm');
            if (form) {
                form.addEventListener('submit', handleFormSubmit);
                console.log('Form event listener attached');
            } else {
                console.error('Form not found');
            }
        });

        function initializePage() {
            console.log('Initializing page for role:', currentRole);
            
            const titleElement = document.getElementById('loginTitle');
            const subtitleElement = document.getElementById('loginSubtitle');
            const clinicGroup = document.getElementById('clinicGroup');

            switch(currentRole) {
                case 'admin':
                    titleElement.textContent = '管理员登录';
                    subtitleElement.textContent = '请输入管理员密码';
                    break;
                case 'reception':
                    titleElement.textContent = '前台登录';
                    subtitleElement.textContent = '请输入前台密码';
                    break;
                case 'doctor':
                    titleElement.textContent = '医助登录';
                    subtitleElement.textContent = '请选择诊室并输入密码';
                    clinicGroup.style.display = 'block';
                    loadClinics();
                    break;
                default:
                    window.location.href = 'index.html';
                    return;
            }
        }

        async function loadClinics() {
            try {
                console.log('Loading clinics...');
                const response = await apiRequest('/clinics');
                const clinicSelect = document.getElementById('clinicId');
                
                clinicSelect.innerHTML = '<option value="">请选择诊室</option>';
                
                response.forEach(clinic => {
                    const option = document.createElement('option');
                    option.value = clinic.id;
                    option.textContent = clinic.name;
                    clinicSelect.appendChild(option);
                });
                console.log('Clinics loaded successfully');
            } catch (error) {
                console.error('Failed to load clinics:', error);
                showMessage('加载诊室列表失败: ' + error.message, 'error');
            }
        }

        async function handleFormSubmit(event) {
            event.preventDefault();
            console.log('Form submitted');
            
            const loginBtn = document.getElementById('loginBtn');
            const originalText = loginBtn.textContent;
            
            // 显示加载状态
            loginBtn.textContent = '登录中...';
            loginBtn.disabled = true;

            try {
                const formData = new FormData(event.target);
                const password = formData.get('password');
                const clinicId = formData.get('clinicId');

                console.log('Form data:', { password: password ? '***' : 'empty', clinicId, currentRole });

                let endpoint = '';
                let requestData = { password };

                switch(currentRole) {
                    case 'admin':
                        endpoint = '/auth/admin/login';
                        break;
                    case 'reception':
                        endpoint = '/auth/reception/login';
                        break;
                    case 'doctor':
                        if (!clinicId) {
                            throw new Error('请选择诊室');
                        }
                        endpoint = '/auth/doctor/login';
                        requestData.clinicId = clinicId;
                        break;
                }

                console.log('Making request to:', endpoint);

                const response = await apiRequest(endpoint, {
                    method: 'POST',
                    body: JSON.stringify(requestData)
                });

                console.log('Login successful:', response);

                // 保存登录信息
                setToken(response.token);
                setUserInfo(response.role, response.clinic || {});

                showMessage('登录成功', 'success');

                // 跳转到对应页面
                setTimeout(() => {
                    switch(response.role) {
                        case 'admin':
                            window.location.href = 'admin.html';
                            break;
                        case 'reception':
                            window.location.href = 'reception.html';
                            break;
                        case 'doctor':
                            window.location.href = 'doctor.html';
                            break;
                    }
                }, 1000);

            } catch (error) {
                console.error('Login error:', error);
                showMessage('登录失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                loginBtn.textContent = originalText;
                loginBtn.disabled = false;
            }
        }
    </script>
</body>
</html>
