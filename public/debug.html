<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <h1>系统调试页面</h1>
    
    <div class="test-section">
        <h2>1. 测试common.js函数</h2>
        <button onclick="testCommonFunctions()">测试通用函数</button>
        <div id="commonResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 测试API连接</h2>
        <button onclick="testApiConnection()">测试API连接</button>
        <div id="apiResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 测试登录API</h2>
        <button onclick="testLogin()">测试管理员登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 测试页面元素</h2>
        <button onclick="testPageElements()">检查页面元素</button>
        <div id="elementsResult" class="result"></div>
    </div>

    <script src="scripts/common.js"></script>
    <script>
        function testCommonFunctions() {
            const result = document.getElementById('commonResult');
            let output = '';
            
            try {
                // 测试各种函数是否存在
                const functions = ['getUrlParameter', 'getToken', 'setToken', 'apiRequest', 'showMessage', 'showLoading', 'hideLoading'];
                
                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        output += `✓ ${funcName} 函数存在<br>`;
                    } else {
                        output += `✗ ${funcName} 函数不存在<br>`;
                    }
                });
                
                // 测试URL参数解析
                const testParam = getUrlParameter('test');
                output += `URL参数测试: ${testParam || '无参数'}<br>`;
                
                // 测试localStorage
                setToken('test-token');
                const token = getToken();
                output += `Token测试: ${token === 'test-token' ? '✓ 正常' : '✗ 异常'}<br>`;
                
                result.innerHTML = output;
            } catch (error) {
                result.innerHTML = `错误: ${error.message}`;
            }
        }
        
        async function testApiConnection() {
            const result = document.getElementById('apiResult');
            result.innerHTML = '测试中...';
            
            try {
                const response = await fetch('/api/clinics');
                const data = await response.json();
                
                result.innerHTML = `
                    状态码: ${response.status}<br>
                    响应: ${JSON.stringify(data, null, 2)}
                `;
            } catch (error) {
                result.innerHTML = `连接错误: ${error.message}`;
            }
        }
        
        async function testLogin() {
            const result = document.getElementById('loginResult');
            result.innerHTML = '登录测试中...';
            
            try {
                const response = await apiRequest('/auth/admin/login', {
                    method: 'POST',
                    body: JSON.stringify({ password: 'admin123' })
                });
                
                result.innerHTML = `
                    登录成功!<br>
                    Token: ${response.token}<br>
                    Role: ${response.role}
                `;
            } catch (error) {
                result.innerHTML = `登录失败: ${error.message}`;
            }
        }
        
        function testPageElements() {
            const result = document.getElementById('elementsResult');
            let output = '';
            
            // 检查关键元素
            const elements = ['loginForm', 'loginBtn', 'password'];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    output += `✓ 元素 ${id} 存在<br>`;
                } else {
                    output += `✗ 元素 ${id} 不存在<br>`;
                }
            });
            
            // 检查事件监听器
            const form = document.getElementById('loginForm');
            if (form) {
                output += `表单元素: ${form.tagName}<br>`;
                output += `表单事件: ${form.onsubmit ? '有onsubmit' : '无onsubmit'}<br>`;
            }
            
            result.innerHTML = output;
        }
        
        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('调试页面加载完成');
            testCommonFunctions();
        });
    </script>
</body>
</html>
