<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前台管理 - 优眠医学中心</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/reception.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>前台管理系统</h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="refreshOverview()">刷新</button>
                <button class="btn btn-secondary" onclick="logout()">退出登录</button>
            </div>
        </header>

        <main class="main">
            <div class="reception-tabs">
                <button class="tab-btn active" onclick="showTab('overview')">排队概览</button>
                <button class="tab-btn" onclick="showTab('search')">患者查询</button>
            </div>

            <!-- 排队概览 -->
            <div id="overview-tab" class="tab-content active">
                <div class="section-header">
                    <h2>各诊室排队情况</h2>
                    <div class="overview-stats" id="overviewStats">
                        <div class="stat-item">
                            <span class="stat-number">0</span>
                            <span class="stat-label">总排队人数</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">0</span>
                            <span class="stat-label">活跃诊室</span>
                        </div>
                    </div>
                </div>

                <div class="clinics-overview" id="clinicsOverview">
                    <!-- 诊室排队情况将在这里显示 -->
                </div>
            </div>

            <!-- 患者查询 -->
            <div id="search-tab" class="tab-content">
                <div class="section-header">
                    <h2>患者查询</h2>
                </div>

                <div class="search-form">
                    <form id="patientSearchForm">
                        <div class="form-group">
                            <label for="searchPhone">手机号</label>
                            <input type="tel" id="searchPhone" name="phone" required placeholder="请输入患者手机号">
                        </div>
                        <button type="submit" class="btn btn-primary" id="searchBtn">
                            查询
                        </button>
                    </form>
                </div>

                <div class="search-result" id="searchResult">
                    <!-- 查询结果将在这里显示 -->
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 优眠医学中心. All rights reserved.</p>
        </footer>
    </div>

    <script src="scripts/common.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            if (!checkAuth('reception')) {
                return;
            }

            // 初始化页面
            loadOverview();
            
            // 绑定表单事件
            document.getElementById('patientSearchForm').addEventListener('submit', handleSearch);
            
            // 自动刷新排队信息
            setInterval(loadOverview, 30000); // 每30秒刷新一次
        });

        // 显示标签页
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活对应的标签按钮
            event.target.classList.add('active');
            
            // 根据标签加载相应数据
            if (tabName === 'overview') {
                loadOverview();
            }
        }

        // 加载排队概览
        async function loadOverview() {
            try {
                const response = await apiRequest('/queue/overview');
                const clinicsOverview = document.getElementById('clinicsOverview');
                const overviewStats = document.getElementById('overviewStats');
                
                // 计算统计数据
                let totalQueue = 0;
                let activeClinics = 0;
                
                response.forEach(item => {
                    totalQueue += item.queueCount;
                    if (item.queueCount > 0) {
                        activeClinics++;
                    }
                });
                
                // 更新统计信息
                overviewStats.innerHTML = `
                    <div class="stat-item">
                        <span class="stat-number">${totalQueue}</span>
                        <span class="stat-label">总排队人数</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${activeClinics}</span>
                        <span class="stat-label">有排队诊室</span>
                    </div>
                `;
                
                if (response.length === 0) {
                    clinicsOverview.innerHTML = '<div class="card"><p>暂无诊室信息。</p></div>';
                    return;
                }
                
                clinicsOverview.innerHTML = response.map(item => `
                    <div class="clinic-overview-card">
                        <div class="clinic-header">
                            <h3>${item.clinic.name}</h3>
                            <span class="queue-count ${item.queueCount > 0 ? 'has-queue' : ''}">${item.queueCount}人排队</span>
                        </div>
                        <div class="clinic-description">
                            <p>${item.clinic.description || '暂无描述'}</p>
                        </div>
                        <div class="queue-list">
                            ${item.queue.length > 0 ? item.queue.map((queue, index) => `
                                <div class="queue-item ${index === 0 ? 'current' : ''}">
                                    <div class="queue-item-info">
                                        <span class="queue-number">#${queue.queue_number}</span>
                                        <span class="patient-name">${queue.patients.name}</span>
                                        <span class="patient-phone">${queue.patients.phone}</span>
                                    </div>
                                    <div class="queue-time">
                                        <div>${formatDateTime(queue.created_at)}</div>
                                        <div class="queue-duration">${formatQueueTime(queue.queue_time_minutes)}</div>
                                    </div>
                                    ${index === 0 ? '<div class="current-label">当前</div>' : ''}
                                </div>
                            `).join('') : '<div class="no-queue">暂无排队</div>'}
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                showMessage('加载排队概览失败: ' + error.message, 'error');
            }
        }

        // 刷新概览
        function refreshOverview() {
            loadOverview();
            showMessage('排队信息已刷新', 'success');
        }

        // 处理患者查询
        async function handleSearch(event) {
            event.preventDefault();
            
            const searchBtn = document.getElementById('searchBtn');
            const originalText = searchBtn.textContent;
            showLoading(searchBtn, '查询中...');

            try {
                const formData = new FormData(event.target);
                const phone = formData.get('phone');

                const response = await apiRequest(`/queue/status/${phone}`);
                
                showSearchResult(response);

            } catch (error) {
                showMessage('查询失败: ' + error.message, 'error');
                document.getElementById('searchResult').innerHTML = '';
            } finally {
                hideLoading(searchBtn, originalText);
            }
        }

        // 显示查询结果
        function showSearchResult(data) {
            const searchResult = document.getElementById('searchResult');
            
            if (data.queues.length === 0) {
                searchResult.innerHTML = `
                    <div class="search-result-card">
                        <h3>患者信息</h3>
                        <div class="patient-info">
                            <p><strong>姓名：</strong>${data.patient.name}</p>
                            <p><strong>手机号：</strong>${data.patient.phone}</p>
                        </div>
                        <div class="no-queue">
                            <p>该患者当前没有排队记录</p>
                        </div>
                    </div>
                `;
                return;
            }

            searchResult.innerHTML = `
                <div class="search-result-card">
                    <h3>患者信息</h3>
                    <div class="patient-info">
                        <p><strong>姓名：</strong>${data.patient.name}</p>
                        <p><strong>手机号：</strong>${data.patient.phone}</p>
                    </div>
                    <h4>排队记录</h4>
                    <div class="queue-records">
                        ${data.queues.map(queue => `
                            <div class="queue-record">
                                <div class="queue-record-header">
                                    <span class="queue-number">#${queue.queue_number}</span>
                                    <span class="clinic-name">${queue.clinics.name}</span>
                                    <span class="queue-status">${formatQueuePosition(queue.people_ahead)}</span>
                                </div>
                                <div class="queue-record-info">
                                    <p><strong>排队时长：</strong>${formatQueueTime(queue.queue_time_minutes)}</p>
                                    <p><strong>排队时间：</strong>${formatDateTime(queue.created_at)}</p>
                                    <p><strong>诊室描述：</strong>${queue.clinics.description || '暂无描述'}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
