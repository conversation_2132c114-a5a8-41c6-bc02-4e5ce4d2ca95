<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小登录页面</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 50px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .login-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .form-group { 
            margin-bottom: 20px; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold;
        }
        input { 
            width: 100%; 
            padding: 12px; 
            border: 1px solid #ddd; 
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button { 
            width: 100%;
            padding: 12px; 
            background: #2196F3; 
            color: white; 
            border: none; 
            border-radius: 6px;
            cursor: pointer; 
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background: #1976D2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>管理员登录</h1>
        
        <div id="message"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit" id="loginBtn">登录</button>
        </form>
        
        <p style="text-align: center; margin-top: 20px;">
            <a href="index.html" style="color: #666;">返回首页</a>
        </p>
    </div>

    <script>
        console.log('页面加载完成');
        
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;
            
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 3000);
        }
        
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            console.log('表单提交');
            
            const loginBtn = document.getElementById('loginBtn');
            const password = document.getElementById('password').value;
            
            console.log('密码:', password);
            
            // 禁用按钮
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                console.log('发送登录请求...');
                
                const response = await fetch('/api/auth/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ password: password })
                });
                
                console.log('响应状态:', response.status);
                
                const data = await response.json();
                console.log('响应数据:', data);
                
                if (response.ok) {
                    // 保存token
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('userRole', data.role);
                    
                    showMessage('登录成功！正在跳转...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = 'admin.html';
                    }, 1500);
                } else {
                    showMessage('登录失败: ' + data.error, 'error');
                }
                
            } catch (error) {
                console.error('请求错误:', error);
                showMessage('网络错误: ' + error.message, 'error');
            } finally {
                // 恢复按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });
        
        // 测试按钮点击
        document.getElementById('loginBtn').addEventListener('click', function() {
            console.log('按钮被点击');
        });
        
        console.log('事件监听器已绑定');
    </script>
</body>
</html>
