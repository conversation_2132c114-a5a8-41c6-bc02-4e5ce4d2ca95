<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, button { padding: 10px; font-size: 16px; }
        button { background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>登录功能测试</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123" required>
        </div>
        <button type="submit">测试管理员登录</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>

    <script>
        const API_BASE = '/api';
        
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在登录...';
            
            try {
                const response = await fetch(`${API_BASE}/auth/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h3>登录成功!</h3>
                        <p><strong>Token:</strong> ${data.token}</p>
                        <p><strong>Role:</strong> ${data.role}</p>
                    `;
                    resultDiv.style.backgroundColor = '#d4edda';
                    resultDiv.style.color = '#155724';
                } else {
                    resultDiv.innerHTML = `
                        <h3>登录失败!</h3>
                        <p><strong>错误:</strong> ${data.error}</p>
                    `;
                    resultDiv.style.backgroundColor = '#f8d7da';
                    resultDiv.style.color = '#721c24';
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>请求失败!</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                `;
                resultDiv.style.backgroundColor = '#f8d7da';
                resultDiv.style.color = '#721c24';
            }
        });
    </script>
</body>
</html>
