<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单登录测试</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/login.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>优眠医学中心</h1>
            <p>管理员登录测试</p>
        </header>

        <main class="main">
            <div class="login-form">
                <div class="login-header">
                    <h2>管理员登录</h2>
                    <p>请输入管理员密码</p>
                </div>

                <form id="loginForm">
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" value="admin123" required>
                    </div>

                    <button type="submit" class="btn btn-primary" id="loginBtn">
                        登录
                    </button>
                </form>

                <div class="form-footer">
                    <a href="index.html" class="btn btn-secondary">返回首页</a>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 优眠医学中心. All rights reserved.</p>
        </footer>
    </div>

    <script>
        // 简化的API请求函数
        async function simpleApiRequest(endpoint, options = {}) {
            const url = `/api${endpoint}`;
            
            try {
                console.log('Making request to:', url, 'with options:', options);
                const response = await fetch(url, options);
                const data = await response.json();
                
                console.log('Response:', response.status, data);
                
                if (!response.ok) {
                    throw new Error(data.error || '请求失败');
                }
                
                return data;
            } catch (error) {
                console.error('API请求错误:', error);
                throw error;
            }
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }
            
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            const container = document.querySelector('.container');
            if (container) {
                container.insertBefore(alert, container.firstChild);
                
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 3000);
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded');
            
            document.getElementById('loginForm').addEventListener('submit', async function(event) {
                event.preventDefault();
                
                console.log('Form submitted');
                
                const loginBtn = document.getElementById('loginBtn');
                const originalText = loginBtn.textContent;
                
                loginBtn.textContent = '登录中...';
                loginBtn.disabled = true;

                try {
                    const password = document.getElementById('password').value;
                    console.log('Password:', password);

                    const response = await simpleApiRequest('/auth/admin/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ password })
                    });

                    console.log('Login successful:', response);
                    
                    // 保存token
                    localStorage.setItem('token', response.token);
                    localStorage.setItem('userRole', response.role);

                    showMessage('登录成功', 'success');

                    // 跳转到管理员页面
                    setTimeout(() => {
                        window.location.href = 'admin.html';
                    }, 1000);

                } catch (error) {
                    console.error('Login failed:', error);
                    showMessage('登录失败: ' + error.message, 'error');
                } finally {
                    loginBtn.textContent = originalText;
                    loginBtn.disabled = false;
                }
            });
        });
    </script>
</body>
</html>
