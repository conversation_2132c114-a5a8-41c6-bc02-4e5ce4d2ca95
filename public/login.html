<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小登录页面</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 50px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .login-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .form-group { 
            margin-bottom: 20px; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button { 
            width: 100%;
            padding: 12px; 
            background: #2196F3; 
            color: white; 
            border: none; 
            border-radius: 6px;
            cursor: pointer; 
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background: #1976D2;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1 id="loginTitle">用户登录</h1>

        <div id="message"></div>

        <form id="loginForm">
            <!-- 诊室选择（仅医助显示） -->
            <div class="form-group" id="clinicGroup" style="display: none;">
                <label for="clinicId">选择诊室:</label>
                <select id="clinicId" name="clinicId" required>
                    <option value="">请选择诊室</option>
                </select>
            </div>

            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>

            <button type="submit" id="loginBtn">登录</button>
        </form>

        <p style="text-align: center; margin-top: 20px;">
            <a href="index.html" style="color: #666;">返回首页</a>
        </p>
    </div>

    <script>
        let currentRole = '';

        console.log('页面加载完成');

        // 获取URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;

            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 3000);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 获取角色参数
            currentRole = getUrlParam('role');
            console.log('当前角色:', currentRole);

            if (!currentRole) {
                window.location.href = 'index.html';
                return;
            }

            // 根据角色设置页面
            initializePage();
        });

        function initializePage() {
            const titleElement = document.getElementById('loginTitle');
            const clinicGroup = document.getElementById('clinicGroup');
            const clinicSelect = document.getElementById('clinicId');
            const passwordInput = document.getElementById('password');

            switch(currentRole) {
                case 'admin':
                    titleElement.textContent = '管理员登录';
                    passwordInput.value = 'admin123'; // 开发时方便测试
                    clinicGroup.style.display = 'none';
                    clinicSelect.removeAttribute('required'); // 移除required属性
                    break;
                case 'reception':
                    titleElement.textContent = '前台登录';
                    passwordInput.value = 'reception123'; // 开发时方便测试
                    clinicGroup.style.display = 'none';
                    clinicSelect.removeAttribute('required'); // 移除required属性
                    break;
                case 'doctor':
                    titleElement.textContent = '医助登录';
                    clinicGroup.style.display = 'block';
                    clinicSelect.setAttribute('required', 'required'); // 添加required属性
                    loadClinics();
                    break;
                default:
                    window.location.href = 'index.html';
                    return;
            }
        }

        async function loadClinics() {
            try {
                console.log('加载诊室列表...');
                const response = await fetch('/api/clinics');
                const data = await response.json();

                const clinicSelect = document.getElementById('clinicId');
                clinicSelect.innerHTML = '<option value="">请选择诊室</option>';

                if (response.ok && data.length > 0) {
                    data.forEach(clinic => {
                        const option = document.createElement('option');
                        option.value = clinic.id;
                        option.textContent = clinic.name;
                        clinicSelect.appendChild(option);
                    });
                } else {
                    showMessage('暂无可用诊室', 'error');
                }
            } catch (error) {
                console.error('加载诊室失败:', error);
                showMessage('加载诊室列表失败', 'error');
            }
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            console.log('表单提交');

            const loginBtn = document.getElementById('loginBtn');
            const password = document.getElementById('password').value;
            const clinicId = document.getElementById('clinicId').value;

            console.log('登录数据:', { role: currentRole, password: '***', clinicId });

            // 禁用按钮
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';

            try {
                let endpoint = '';
                let requestData = { password };

                switch(currentRole) {
                    case 'admin':
                        endpoint = '/api/auth/admin/login';
                        break;
                    case 'reception':
                        endpoint = '/api/auth/reception/login';
                        break;
                    case 'doctor':
                        if (!clinicId) {
                            throw new Error('请选择诊室');
                        }
                        endpoint = '/api/auth/doctor/login';
                        requestData.clinicId = clinicId;
                        break;
                    default:
                        throw new Error('未知角色');
                }

                console.log('发送请求到:', endpoint);

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('响应状态:', response.status);

                const data = await response.json();
                console.log('响应数据:', data);

                if (response.ok) {
                    // 保存token和用户信息
                    localStorage.setItem('token', data.token);
                    localStorage.setItem('userRole', data.role);
                    if (data.clinic) {
                        localStorage.setItem('userInfo', JSON.stringify(data.clinic));
                    }

                    showMessage('登录成功！正在跳转...', 'success');

                    // 根据角色跳转到对应页面
                    setTimeout(() => {
                        switch(data.role) {
                            case 'admin':
                                window.location.href = 'admin.html';
                                break;
                            case 'reception':
                                window.location.href = 'reception.html';
                                break;
                            case 'doctor':
                                window.location.href = 'doctor.html';
                                break;
                        }
                    }, 1500);
                } else {
                    showMessage('登录失败: ' + data.error, 'error');
                }

            } catch (error) {
                console.error('请求错误:', error);
                showMessage('登录失败: ' + error.message, 'error');
            } finally {
                // 恢复按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        });

        console.log('事件监听器已绑定');
    </script>
</body>
</html>
