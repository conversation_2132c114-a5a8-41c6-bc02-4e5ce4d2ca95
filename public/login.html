<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 优眠医学中心</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/login.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>优眠医学中心</h1>
            <p>用户登录</p>
        </header>

        <main class="main">
            <div class="login-form">
                <div class="login-header">
                    <h2 id="loginTitle">登录</h2>
                    <p id="loginSubtitle">请输入您的登录信息</p>
                </div>

                <form id="loginForm">
                    <!-- 诊室选择（仅医助显示） -->
                    <div class="form-group" id="clinicGroup" style="display: none;">
                        <label for="clinicId">选择诊室</label>
                        <select id="clinicId" name="clinicId" required>
                            <option value="">请选择诊室</option>
                        </select>
                    </div>

                    <!-- 密码输入 -->
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required>
                    </div>

                    <button type="submit" class="btn btn-primary" id="loginBtn">
                        登录
                    </button>
                </form>

                <div class="login-footer">
                    <a href="index.html" class="btn btn-secondary">返回首页</a>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 优眠医学中心. All rights reserved.</p>
        </footer>
    </div>

    <script src="scripts/common.js"></script>
    <script>
        let currentRole = '';

        document.addEventListener('DOMContentLoaded', function() {
            // 获取角色参数
            currentRole = getUrlParameter('role');
            
            if (!currentRole) {
                window.location.href = 'index.html';
                return;
            }

            initializeLogin();
        });

        function initializeLogin() {
            const titleElement = document.getElementById('loginTitle');
            const subtitleElement = document.getElementById('loginSubtitle');
            const clinicGroup = document.getElementById('clinicGroup');

            switch(currentRole) {
                case 'admin':
                    titleElement.textContent = '管理员登录';
                    subtitleElement.textContent = '请输入管理员密码';
                    break;
                case 'reception':
                    titleElement.textContent = '前台登录';
                    subtitleElement.textContent = '请输入前台密码';
                    break;
                case 'doctor':
                    titleElement.textContent = '医助登录';
                    subtitleElement.textContent = '请选择诊室并输入密码';
                    clinicGroup.style.display = 'block';
                    loadClinics();
                    break;
                default:
                    window.location.href = 'index.html';
                    return;
            }

            // 绑定表单提交事件
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
        }

        async function loadClinics() {
            try {
                const response = await apiRequest('/clinics');
                const clinicSelect = document.getElementById('clinicId');
                
                clinicSelect.innerHTML = '<option value="">请选择诊室</option>';
                
                response.forEach(clinic => {
                    const option = document.createElement('option');
                    option.value = clinic.id;
                    option.textContent = clinic.name;
                    clinicSelect.appendChild(option);
                });
            } catch (error) {
                showMessage('加载诊室列表失败: ' + error.message, 'error');
            }
        }

        async function handleLogin(event) {
            event.preventDefault();

            console.log('Login form submitted');

            const loginBtn = document.getElementById('loginBtn');
            const originalText = loginBtn.textContent;

            showLoading(loginBtn, '登录中...');

            try {
                const formData = new FormData(event.target);
                const password = formData.get('password');
                const clinicId = formData.get('clinicId');

                console.log('Form data:', { password, clinicId, currentRole });

                let endpoint = '';
                let requestData = { password };

                switch(currentRole) {
                    case 'admin':
                        endpoint = '/auth/admin/login';
                        break;
                    case 'reception':
                        endpoint = '/auth/reception/login';
                        break;
                    case 'doctor':
                        if (!clinicId) {
                            throw new Error('请选择诊室');
                        }
                        endpoint = '/auth/doctor/login';
                        requestData.clinicId = clinicId;
                        break;
                }

                console.log('Making API request to:', endpoint, 'with data:', requestData);

                const response = await apiRequest(endpoint, {
                    method: 'POST',
                    body: JSON.stringify(requestData)
                });

                console.log('Login response:', response);

                // 保存登录信息
                setToken(response.token);
                setUserInfo(response.role, response.clinic || {});

                showMessage('登录成功', 'success');

                // 跳转到对应页面
                setTimeout(() => {
                    switch(response.role) {
                        case 'admin':
                            window.location.href = 'admin.html';
                            break;
                        case 'reception':
                            window.location.href = 'reception.html';
                            break;
                        case 'doctor':
                            window.location.href = 'doctor.html';
                            break;
                    }
                }, 1000);

            } catch (error) {
                console.error('Login error:', error);
                showMessage('登录失败: ' + error.message, 'error');
            } finally {
                hideLoading(loginBtn, originalText);
            }
        }
    </script>
</body>
</html>
