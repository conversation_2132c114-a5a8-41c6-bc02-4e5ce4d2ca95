<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口文档 - 优眠医学中心排队叫号系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 12px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav h2 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .nav ul {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        
        .nav a {
            color: #2196F3;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .nav a:hover {
            background: #e3f2fd;
        }
        
        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: #2196F3;
            color: white;
            padding: 20px;
        }
        
        .section-header h2 {
            margin: 0;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .endpoint {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .endpoint-header {
            background: #f5f5f5;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .method.get { background: #4CAF50; color: white; }
        .method.post { background: #2196F3; color: white; }
        .method.put { background: #FF9800; color: white; }
        .method.delete { background: #f44336; color: white; }
        
        .endpoint-url {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        
        .endpoint-body {
            padding: 15px;
        }
        
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .param-table th,
        .param-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .param-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .required {
            color: #f44336;
            font-weight: bold;
        }
        
        .optional {
            color: #666;
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .response-example {
            background: #e8f5e8;
            border-left: 4px solid #4CAF50;
            padding: 10px;
            margin: 10px 0;
        }
        
        .error-example {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>API接口文档</h1>
            <p>优眠医学中心排队叫号系统</p>
        </div>
        
        <div class="nav">
            <h2>快速导航</h2>
            <ul>
                <li><a href="#auth">认证接口</a></li>
                <li><a href="#clinics">诊室管理</a></li>
                <li><a href="#queue">排队管理</a></li>
                <li><a href="#admin">管理员接口</a></li>
            </ul>
        </div>
        
        <!-- 认证接口 -->
        <div class="section" id="auth">
            <div class="section-header">
                <h2>认证接口</h2>
            </div>
            <div class="section-content">
                
                <!-- 管理员登录 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/api/auth/admin/login</span>
                        <span style="float: right;">管理员登录</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>管理员密码</td>
                            </tr>
                        </table>
                        
                        <h4>请求示例</h4>
                        <div class="code-block">
{
  "password": "admin123"
}
                        </div>
                        
                        <h4>响应示例</h4>
                        <div class="response-example">
                            <strong>成功响应 (200)</strong>
                            <div class="code-block">
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "role": "admin"
}
                            </div>
                        </div>
                        
                        <div class="error-example">
                            <strong>错误响应 (401)</strong>
                            <div class="code-block">
{
  "error": "密码错误"
}
                            </div>
                        </div>
                        
                        <button class="test-button" onclick="testAPI('/api/auth/admin/login', 'POST', {password: 'admin123'})">测试接口</button>
                    </div>
                </div>
                
                <!-- 前台登录 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/api/auth/reception/login</span>
                        <span style="float: right;">前台登录</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>前台密码</td>
                            </tr>
                        </table>
                        
                        <button class="test-button" onclick="testAPI('/api/auth/reception/login', 'POST', {password: 'reception123'})">测试接口</button>
                    </div>
                </div>
                
                <!-- 医助登录 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/api/auth/doctor/login</span>
                        <span style="float: right;">医助登录</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>clinicId</td>
                                <td>number</td>
                                <td class="required">是</td>
                                <td>诊室ID</td>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>诊室密码</td>
                            </tr>
                        </table>
                        
                        <h4>请求示例</h4>
                        <div class="code-block">
{
  "clinicId": 1,
  "password": "clinic123"
}
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
        
        <!-- 诊室管理接口 -->
        <div class="section" id="clinics">
            <div class="section-header">
                <h2>诊室管理接口</h2>
            </div>
            <div class="section-content">
                
                <!-- 获取诊室列表 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/api/clinics</span>
                        <span style="float: right;">获取诊室列表</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>响应示例</h4>
                        <div class="response-example">
                            <div class="code-block">
[
  {
    "id": 1,
    "name": "内科诊室",
    "description": "内科疾病诊疗",
    "active": true
  },
  {
    "id": 2,
    "name": "外科诊室", 
    "description": "外科疾病诊疗",
    "active": true
  }
]
                            </div>
                        </div>
                        
                        <button class="test-button" onclick="testAPI('/api/clinics', 'GET')">测试接口</button>
                    </div>
                </div>
                
                <!-- 创建诊室 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/api/clinics</span>
                        <span style="float: right;">创建诊室 (需要管理员权限)</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求头</h4>
                        <div class="code-block">
Authorization: Bearer {token}
                        </div>
                        
                        <h4>请求参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>name</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>诊室名称</td>
                            </tr>
                            <tr>
                                <td>description</td>
                                <td>string</td>
                                <td class="optional">否</td>
                                <td>诊室描述</td>
                            </tr>
                            <tr>
                                <td>password</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>诊室密码</td>
                            </tr>
                        </table>
                        
                        <h4>请求示例</h4>
                        <div class="code-block">
{
  "name": "心内科诊室",
  "description": "心血管疾病专科诊疗",
  "password": "clinic123"
}
                        </div>
                    </div>
                </div>
                
                <!-- 生成诊室二维码 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/api/clinics/{id}/qrcode</span>
                        <span style="float: right;">生成诊室二维码 (需要管理员权限)</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>路径参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>id</td>
                                <td>number</td>
                                <td>诊室ID</td>
                            </tr>
                        </table>
                        
                        <h4>响应示例</h4>
                        <div class="response-example">
                            <div class="code-block">
{
  "success": true,
  "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "url": "http://localhost:3000/patient.html?clinic=1",
  "clinic": {
    "id": 1,
    "name": "内科诊室"
  }
}
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>

        <!-- 排队管理接口 -->
        <div class="section" id="queue">
            <div class="section-header">
                <h2>排队管理接口</h2>
            </div>
            <div class="section-content">

                <!-- 患者排队 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/api/queue/join</span>
                        <span style="float: right;">患者排队</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>clinicId</td>
                                <td>number</td>
                                <td class="required">是</td>
                                <td>诊室ID</td>
                            </tr>
                            <tr>
                                <td>name</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>患者姓名</td>
                            </tr>
                            <tr>
                                <td>phone</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>患者手机号</td>
                            </tr>
                        </table>

                        <h4>请求示例</h4>
                        <div class="code-block">
{
  "clinicId": 1,
  "name": "张三",
  "phone": "13800138000"
}
                        </div>

                        <h4>响应示例</h4>
                        <div class="response-example">
                            <div class="code-block">
{
  "success": true,
  "message": "排队成功",
  "queue": {
    "id": 1,
    "clinic_id": 1,
    "patient_id": 1,
    "queue_number": 1,
    "status": "waiting",
    "created_at": "2024-01-01T10:00:00Z",
    "patients": {
      "name": "张三",
      "phone": "13800138000"
    },
    "clinics": {
      "name": "内科诊室"
    }
  }
}
                            </div>
                        </div>

                        <button class="test-button" onclick="testAPI('/api/queue/join', 'POST', {clinicId: 1, name: '测试患者', phone: '13800138000'})">测试接口</button>
                    </div>
                </div>

                <!-- 查询排队状态 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/api/queue/status/{phone}</span>
                        <span style="float: right;">查询排队状态</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>路径参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>phone</td>
                                <td>string</td>
                                <td>患者手机号</td>
                            </tr>
                        </table>

                        <h4>响应示例</h4>
                        <div class="response-example">
                            <div class="code-block">
{
  "success": true,
  "patient": {
    "id": 1,
    "name": "张三",
    "phone": "13800138000"
  },
  "queues": [
    {
      "id": 1,
      "queue_number": 3,
      "status": "waiting",
      "created_at": "2024-01-01T10:00:00Z",
      "people_ahead": 2,
      "queue_time_minutes": 15,
      "clinics": {
        "name": "内科诊室",
        "description": "内科疾病诊疗"
      }
    }
  ]
}
                            </div>
                        </div>

                        <button class="test-button" onclick="testAPI('/api/queue/status/13800138000', 'GET')">测试接口</button>
                    </div>
                </div>

                <!-- 获取诊室排队情况 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/api/queue/clinic/{clinicId}</span>
                        <span style="float: right;">获取诊室排队情况 (需要医助权限)</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求头</h4>
                        <div class="code-block">
Authorization: Bearer {token}
                        </div>

                        <h4>路径参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>clinicId</td>
                                <td>number</td>
                                <td>诊室ID</td>
                            </tr>
                        </table>

                        <h4>响应示例</h4>
                        <div class="response-example">
                            <div class="code-block">
{
  "success": true,
  "queue": [
    {
      "id": 1,
      "queue_number": 1,
      "status": "waiting",
      "created_at": "2024-01-01T10:00:00Z",
      "queue_time_minutes": 15,
      "patients": {
        "name": "张三",
        "phone": "13800138000"
      }
    }
  ]
}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 完成看诊 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/api/queue/complete/{queueId}</span>
                        <span style="float: right;">完成看诊 (需要医助权限)</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求头</h4>
                        <div class="code-block">
Authorization: Bearer {token}
                        </div>

                        <h4>路径参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>queueId</td>
                                <td>number</td>
                                <td>排队记录ID</td>
                            </tr>
                        </table>

                        <h4>响应示例</h4>
                        <div class="response-example">
                            <div class="code-block">
{
  "success": true,
  "message": "看诊完成",
  "queue": {
    "id": 1,
    "status": "completed",
    "completed_at": "2024-01-01T10:30:00Z"
  }
}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 获取排队概览 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/api/queue/overview</span>
                        <span style="float: right;">获取排队概览 (需要前台权限)</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求头</h4>
                        <div class="code-block">
Authorization: Bearer {token}
                        </div>

                        <h4>响应示例</h4>
                        <div class="response-example">
                            <div class="code-block">
[
  {
    "clinic": {
      "id": 1,
      "name": "内科诊室",
      "description": "内科疾病诊疗"
    },
    "queueCount": 2,
    "queue": [
      {
        "id": 1,
        "queue_number": 1,
        "queue_time_minutes": 15,
        "patients": {
          "name": "张三",
          "phone": "13800138000"
        }
      }
    ]
  }
]
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- 管理员接口 -->
        <div class="section" id="admin">
            <div class="section-header">
                <h2>管理员接口</h2>
            </div>
            <div class="section-content">

                <!-- 修改管理员密码 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method put">PUT</span>
                        <span class="endpoint-url">/api/admin/password</span>
                        <span style="float: right;">修改管理员密码</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求头</h4>
                        <div class="code-block">
Authorization: Bearer {token}
                        </div>

                        <h4>请求参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>newPassword</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>新密码</td>
                            </tr>
                        </table>

                        <h4>请求示例</h4>
                        <div class="code-block">
{
  "newPassword": "newadmin123"
}
                        </div>
                    </div>
                </div>

                <!-- 修改前台密码 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method put">PUT</span>
                        <span class="endpoint-url">/api/admin/reception/password</span>
                        <span style="float: right;">修改前台密码</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求头</h4>
                        <div class="code-block">
Authorization: Bearer {token}
                        </div>

                        <h4>请求参数</h4>
                        <table class="param-table">
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>newPassword</td>
                                <td>string</td>
                                <td class="required">是</td>
                                <td>新密码</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 获取管理员排队概览 -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/api/admin/queue/overview</span>
                        <span style="float: right;">获取所有诊室排队概览</span>
                    </div>
                    <div class="endpoint-body">
                        <h4>请求头</h4>
                        <div class="code-block">
Authorization: Bearer {token}
                        </div>

                        <h4>响应示例</h4>
                        <div class="response-example">
                            <div class="code-block">
[
  {
    "clinic": {
      "id": 1,
      "name": "内科诊室",
      "description": "内科疾病诊疗",
      "active": true
    },
    "queueCount": 3,
    "queue": [
      {
        "id": 1,
        "queue_number": 1,
        "status": "waiting",
        "created_at": "2024-01-01T10:00:00Z",
        "queue_time_minutes": 25,
        "patients": {
          "name": "张三",
          "phone": "13800138000"
        }
      }
    ]
  }
]
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- 数据模型说明 -->
        <div class="section">
            <div class="section-header">
                <h2>数据模型说明</h2>
            </div>
            <div class="section-content">

                <h3>患者 (Patient)</h3>
                <table class="param-table">
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>id</td>
                        <td>number</td>
                        <td>患者ID</td>
                    </tr>
                    <tr>
                        <td>name</td>
                        <td>string</td>
                        <td>患者姓名</td>
                    </tr>
                    <tr>
                        <td>phone</td>
                        <td>string</td>
                        <td>患者手机号</td>
                    </tr>
                    <tr>
                        <td>created_at</td>
                        <td>string</td>
                        <td>创建时间 (ISO 8601格式)</td>
                    </tr>
                </table>

                <h3>诊室 (Clinic)</h3>
                <table class="param-table">
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>id</td>
                        <td>number</td>
                        <td>诊室ID</td>
                    </tr>
                    <tr>
                        <td>name</td>
                        <td>string</td>
                        <td>诊室名称</td>
                    </tr>
                    <tr>
                        <td>description</td>
                        <td>string</td>
                        <td>诊室描述</td>
                    </tr>
                    <tr>
                        <td>active</td>
                        <td>boolean</td>
                        <td>是否激活</td>
                    </tr>
                </table>

                <h3>排队记录 (Queue)</h3>
                <table class="param-table">
                    <tr>
                        <th>字段名</th>
                        <th>类型</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>id</td>
                        <td>number</td>
                        <td>排队记录ID</td>
                    </tr>
                    <tr>
                        <td>clinic_id</td>
                        <td>number</td>
                        <td>诊室ID</td>
                    </tr>
                    <tr>
                        <td>patient_id</td>
                        <td>number</td>
                        <td>患者ID</td>
                    </tr>
                    <tr>
                        <td>queue_number</td>
                        <td>number</td>
                        <td>排队号码</td>
                    </tr>
                    <tr>
                        <td>status</td>
                        <td>string</td>
                        <td>状态: waiting(等待中), completed(已完成), cancelled(已取消)</td>
                    </tr>
                    <tr>
                        <td>people_ahead</td>
                        <td>number</td>
                        <td>前面排队人数</td>
                    </tr>
                    <tr>
                        <td>queue_time_minutes</td>
                        <td>number</td>
                        <td>排队时长(分钟)</td>
                    </tr>
                    <tr>
                        <td>created_at</td>
                        <td>string</td>
                        <td>排队时间 (ISO 8601格式)</td>
                    </tr>
                    <tr>
                        <td>completed_at</td>
                        <td>string</td>
                        <td>完成时间 (ISO 8601格式)</td>
                    </tr>
                </table>

            </div>
        </div>

        <!-- 错误码说明 -->
        <div class="section">
            <div class="section-header">
                <h2>错误码说明</h2>
            </div>
            <div class="section-content">

                <table class="param-table">
                    <tr>
                        <th>状态码</th>
                        <th>说明</th>
                        <th>示例</th>
                    </tr>
                    <tr>
                        <td>200</td>
                        <td>请求成功</td>
                        <td>正常返回数据</td>
                    </tr>
                    <tr>
                        <td>401</td>
                        <td>未授权</td>
                        <td>密码错误、token无效</td>
                    </tr>
                    <tr>
                        <td>403</td>
                        <td>权限不足</td>
                        <td>角色权限不够</td>
                    </tr>
                    <tr>
                        <td>404</td>
                        <td>资源不存在</td>
                        <td>诊室不存在、患者不存在</td>
                    </tr>
                    <tr>
                        <td>500</td>
                        <td>服务器内部错误</td>
                        <td>数据库连接失败等</td>
                    </tr>
                </table>

            </div>
        </div>

    </div>

    <script>
        async function testAPI(endpoint, method, data = null) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'code-block';
            resultDiv.style.marginTop = '10px';
            resultDiv.innerHTML = '测试中...';
            
            event.target.parentNode.appendChild(resultDiv);
            
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(endpoint, options);
                const result = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>状态码: ${response.status}</strong><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
                if (response.ok) {
                    resultDiv.style.background = '#e8f5e8';
                    resultDiv.style.borderLeft = '4px solid #4CAF50';
                } else {
                    resultDiv.style.background = '#ffebee';
                    resultDiv.style.borderLeft = '4px solid #f44336';
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<strong>错误:</strong> ${error.message}`;
                resultDiv.style.background = '#ffebee';
                resultDiv.style.borderLeft = '4px solid #f44336';
            }
        }
    </script>
</body>
</html>
