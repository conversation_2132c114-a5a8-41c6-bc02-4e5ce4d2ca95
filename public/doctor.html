<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医助管理 - 优眠医学中心</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/doctor.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="clinic-info">
                <h1 id="clinicName">诊室管理</h1>
                <p id="clinicDescription">医助工作台</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="refreshQueue()">刷新</button>
                <button class="btn btn-secondary" onclick="logout()">退出登录</button>
            </div>
        </header>

        <main class="main">
            <div class="doctor-dashboard">
                <!-- 当前患者 -->
                <div class="current-patient-section">
                    <div class="section-header">
                        <h2>当前患者</h2>
                        <div class="queue-stats" id="queueStats">
                            <span class="stat-item">
                                <span class="stat-number">0</span>
                                <span class="stat-label">排队人数</span>
                            </span>
                        </div>
                    </div>

                    <div class="current-patient" id="currentPatient">
                        <!-- 当前患者信息将在这里显示 -->
                    </div>
                </div>

                <!-- 排队列表 -->
                <div class="queue-section">
                    <div class="section-header">
                        <h2>排队列表</h2>
                    </div>

                    <div class="queue-list" id="queueList">
                        <!-- 排队列表将在这里显示 -->
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 优眠医学中心. All rights reserved.</p>
        </footer>
    </div>

    <script src="scripts/common.js"></script>
    <script>
        let currentClinicId = null;
        let queueData = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            if (!checkAuth('doctor')) {
                return;
            }

            // 获取诊室信息
            const userInfo = getUserInfo();
            currentClinicId = userInfo.id;
            
            if (userInfo.name) {
                document.getElementById('clinicName').textContent = userInfo.name;
                document.getElementById('clinicDescription').textContent = userInfo.description || '医助工作台';
            }

            // 初始化页面
            loadQueue();
            
            // 自动刷新排队信息
            setInterval(loadQueue, 15000); // 每15秒刷新一次
        });

        // 加载排队信息
        async function loadQueue() {
            try {
                const response = await apiRequest(`/queue/clinic/${currentClinicId}`);
                queueData = response.queue;
                
                updateQueueDisplay();
            } catch (error) {
                showMessage('加载排队信息失败: ' + error.message, 'error');
            }
        }

        // 更新排队显示
        function updateQueueDisplay() {
            const queueStats = document.getElementById('queueStats');
            const currentPatient = document.getElementById('currentPatient');
            const queueList = document.getElementById('queueList');
            
            // 更新统计信息
            queueStats.innerHTML = `
                <span class="stat-item">
                    <span class="stat-number">${queueData.length}</span>
                    <span class="stat-label">排队人数</span>
                </span>
            `;
            
            // 显示当前患者
            if (queueData.length > 0) {
                const current = queueData[0];
                currentPatient.innerHTML = `
                    <div class="patient-card current">
                        <div class="patient-header">
                            <div class="queue-number">#${current.queue_number}</div>
                            <div class="patient-info">
                                <h3>${current.patients.name}</h3>
                                <p>${current.patients.phone}</p>
                            </div>
                        </div>
                        <div class="patient-time">
                            <p><strong>排队时间：</strong>${formatDateTime(current.created_at)}</p>
                            <p><strong>等待时长：</strong>${formatQueueTime(current.queue_time_minutes)}</p>
                        </div>
                        <div class="patient-actions">
                            <button class="btn btn-primary" onclick="completePatient(${current.id})">
                                完成看诊
                            </button>
                        </div>
                    </div>
                `;
            } else {
                currentPatient.innerHTML = `
                    <div class="no-patient">
                        <div class="no-patient-icon">👥</div>
                        <h3>暂无患者</h3>
                        <p>当前没有患者排队</p>
                    </div>
                `;
            }
            
            // 显示排队列表
            if (queueData.length > 1) {
                const waitingQueue = queueData.slice(1);
                queueList.innerHTML = waitingQueue.map((queue, index) => `
                    <div class="patient-card waiting">
                        <div class="patient-header">
                            <div class="queue-number">#${queue.queue_number}</div>
                            <div class="patient-info">
                                <h4>${queue.patients.name}</h4>
                                <p>${queue.patients.phone}</p>
                            </div>
                            <div class="waiting-position">
                                第${index + 2}位
                            </div>
                        </div>
                        <div class="patient-time">
                            <p><strong>排队时间：</strong>${formatDateTime(queue.created_at)}</p>
                            <p><strong>等待时长：</strong>${formatQueueTime(queue.queue_time_minutes)}</p>
                        </div>
                    </div>
                `).join('');
            } else {
                queueList.innerHTML = `
                    <div class="no-queue">
                        <p>后续暂无排队患者</p>
                    </div>
                `;
            }
        }

        // 完成患者看诊
        async function completePatient(queueId) {
            confirmAction('确定完成当前患者的看诊吗？', async () => {
                try {
                    await apiRequest(`/queue/complete/${queueId}`, {
                        method: 'POST'
                    });
                    
                    showMessage('看诊完成', 'success');
                    loadQueue(); // 重新加载排队信息
                } catch (error) {
                    showMessage('操作失败: ' + error.message, 'error');
                }
            });
        }

        // 刷新排队信息
        function refreshQueue() {
            loadQueue();
            showMessage('排队信息已刷新', 'success');
        }
    </script>
</body>
</html>
