<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优眠医学中心排队叫号系统</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/index.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>优眠医学中心</h1>
            <p>排队叫号系统</p>
        </header>

        <main class="main">
            <div class="role-selection">
                <h2>请选择您的身份</h2>
                
                <div class="role-cards">
                    <div class="role-card" onclick="goToLogin('admin')">
                        <div class="role-icon">👨‍💼</div>
                        <h3>管理员</h3>
                        <p>诊室管理、系统设置</p>
                    </div>

                    <div class="role-card" onclick="goToLogin('reception')">
                        <div class="role-icon">👩‍💻</div>
                        <h3>前台</h3>
                        <p>查看排队情况</p>
                    </div>

                    <div class="role-card" onclick="goToLogin('doctor')">
                        <div class="role-icon">👨‍⚕️</div>
                        <h3>医助</h3>
                        <p>诊室叫号管理</p>
                    </div>

                    <div class="role-card" onclick="goToPatient()">
                        <div class="role-icon">👤</div>
                        <h3>患者</h3>
                        <p>扫码排队、查看状态</p>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 优眠医学中心. All rights reserved.</p>
            <p><a href="api-docs.html" style="color: rgba(255,255,255,0.8);">API接口文档</a></p>
        </footer>
    </div>

    <script src="scripts/common.js"></script>
    <script>
        function goToLogin(role) {
            window.location.href = `login.html?role=${role}`;
        }

        function goToPatient() {
            window.location.href = 'patient.html';
        }
    </script>
</body>
</html>
