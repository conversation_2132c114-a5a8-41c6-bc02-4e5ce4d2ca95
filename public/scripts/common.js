// 通用JavaScript函数

// API基础URL
const API_BASE = '/api';

// 获取URL参数
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// 获取存储的token
function getToken() {
    return localStorage.getItem('token');
}

// 设置token
function setToken(token) {
    localStorage.setItem('token', token);
}

// 清除token
function clearToken() {
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
    localStorage.removeItem('userInfo');
}

// 获取用户角色
function getUserRole() {
    return localStorage.getItem('userRole');
}

// 设置用户信息
function setUserInfo(role, info = {}) {
    localStorage.setItem('userRole', role);
    localStorage.setItem('userInfo', JSON.stringify(info));
}

// 获取用户信息
function getUserInfo() {
    const info = localStorage.getItem('userInfo');
    return info ? JSON.parse(info) : {};
}

// 发送API请求
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE}${endpoint}`;
    const token = getToken();
    
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
        }
    };
    
    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || '请求失败');
        }
        
        return data;
    } catch (error) {
        console.error('API请求错误:', error);
        throw error;
    }
}

// 显示消息
function showMessage(message, type = 'info') {
    // 移除现有消息
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    // 创建新消息
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alert, container.firstChild);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
}

// 显示加载状态
function showLoading(element, text = '加载中...') {
    if (element) {
        element.innerHTML = `<span class="loading"></span> ${text}`;
        element.disabled = true;
    }
}

// 隐藏加载状态
function hideLoading(element, originalText) {
    if (element) {
        element.innerHTML = originalText;
        element.disabled = false;
    }
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化排队时间
function formatQueueTime(minutes) {
    if (minutes < 1) {
        return '刚刚排队';
    } else if (minutes < 60) {
        return `已排队 ${minutes} 分钟`;
    } else {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        if (remainingMinutes === 0) {
            return `已排队 ${hours} 小时`;
        } else {
            return `已排队 ${hours} 小时 ${remainingMinutes} 分钟`;
        }
    }
}

// 格式化排队位置
function formatQueuePosition(peopleAhead) {
    if (peopleAhead === 0) {
        return '您是下一位';
    } else {
        return `前面还有 ${peopleAhead} 人`;
    }
}

// 检查登录状态
function checkAuth(requiredRole = null) {
    const token = getToken();
    const userRole = getUserRole();
    
    if (!token) {
        window.location.href = 'index.html';
        return false;
    }
    
    if (requiredRole && userRole !== requiredRole && userRole !== 'admin') {
        showMessage('权限不足', 'error');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
        return false;
    }
    
    return true;
}

// 退出登录
function logout() {
    clearToken();
    showMessage('已退出登录', 'success');
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1000);
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
