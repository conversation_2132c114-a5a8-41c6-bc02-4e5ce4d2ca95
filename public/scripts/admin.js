// 管理员页面脚本
let currentQRCode = null;

document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    if (!checkAuth('admin')) {
        return;
    }

    // 初始化页面
    loadClinics();
    
    // 绑定表单事件
    document.getElementById('addClinicForm').addEventListener('submit', handleAddClinic);
    document.getElementById('editClinicForm').addEventListener('submit', handleEditClinic);
    document.getElementById('adminPasswordForm').addEventListener('submit', handleChangeAdminPassword);
    document.getElementById('receptionPasswordForm').addEventListener('submit', handleChangeReceptionPassword);
});

// 显示标签页
function showTab(tabName) {
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 移除所有标签按钮的激活状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 显示选中的标签内容
    document.getElementById(tabName + '-tab').classList.add('active');
    
    // 激活对应的标签按钮
    event.target.classList.add('active');
    
    // 根据标签加载相应数据
    switch(tabName) {
        case 'clinics':
            loadClinics();
            break;
        case 'queue':
            loadQueueOverview();
            break;
    }
}

// 加载诊室列表
async function loadClinics() {
    try {
        const response = await apiRequest('/clinics');
        const clinicsGrid = document.getElementById('clinicsGrid');
        
        if (response.length === 0) {
            clinicsGrid.innerHTML = '<div class="card"><p>暂无诊室，请添加诊室。</p></div>';
            return;
        }
        
        clinicsGrid.innerHTML = response.map(clinic => `
            <div class="clinic-card">
                <h3>${clinic.name}</h3>
                <p>${clinic.description || '暂无描述'}</p>
                <div class="clinic-actions">
                    <button class="btn btn-primary" onclick="generateQRCode(${clinic.id}, '${clinic.name}')">
                        生成二维码
                    </button>
                    <button class="btn btn-secondary" onclick="editClinic(${clinic.id})">
                        编辑
                    </button>
                    <button class="btn btn-danger" onclick="deleteClinic(${clinic.id}, '${clinic.name}')">
                        删除
                    </button>
                </div>
            </div>
        `).join('');
    } catch (error) {
        showMessage('加载诊室列表失败: ' + error.message, 'error');
    }
}

// 显示添加诊室模态框
function showAddClinicModal() {
    document.getElementById('addClinicModal').style.display = 'block';
    document.getElementById('addClinicForm').reset();
}

// 处理添加诊室
async function handleAddClinic(event) {
    event.preventDefault();
    
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    showLoading(submitBtn, '添加中...');
    
    try {
        const formData = new FormData(event.target);
        const clinicData = {
            name: formData.get('clinicName') || document.getElementById('clinicName').value,
            description: formData.get('clinicDescription') || document.getElementById('clinicDescription').value,
            password: formData.get('clinicPassword') || document.getElementById('clinicPassword').value
        };
        
        await apiRequest('/clinics', {
            method: 'POST',
            body: JSON.stringify(clinicData)
        });
        
        showMessage('诊室添加成功', 'success');
        closeModal('addClinicModal');
        loadClinics();
    } catch (error) {
        showMessage('添加诊室失败: ' + error.message, 'error');
    } finally {
        hideLoading(submitBtn, originalText);
    }
}

// 编辑诊室
async function editClinic(clinicId) {
    try {
        // 这里简化处理，实际应该先获取诊室详情
        document.getElementById('editClinicId').value = clinicId;
        document.getElementById('editClinicModal').style.display = 'block';
    } catch (error) {
        showMessage('获取诊室信息失败: ' + error.message, 'error');
    }
}

// 处理编辑诊室
async function handleEditClinic(event) {
    event.preventDefault();
    
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    showLoading(submitBtn, '保存中...');
    
    try {
        const clinicId = document.getElementById('editClinicId').value;
        const clinicData = {
            name: document.getElementById('editClinicName').value,
            description: document.getElementById('editClinicDescription').value
        };
        
        const password = document.getElementById('editClinicPassword').value;
        if (password) {
            clinicData.password = password;
        }
        
        await apiRequest(`/clinics/${clinicId}`, {
            method: 'PUT',
            body: JSON.stringify(clinicData)
        });
        
        showMessage('诊室更新成功', 'success');
        closeModal('editClinicModal');
        loadClinics();
    } catch (error) {
        showMessage('更新诊室失败: ' + error.message, 'error');
    } finally {
        hideLoading(submitBtn, originalText);
    }
}

// 删除诊室
function deleteClinic(clinicId, clinicName) {
    confirmAction(`确定要删除诊室"${clinicName}"吗？`, async () => {
        try {
            await apiRequest(`/clinics/${clinicId}`, {
                method: 'DELETE'
            });
            
            showMessage('诊室删除成功', 'success');
            loadClinics();
        } catch (error) {
            showMessage('删除诊室失败: ' + error.message, 'error');
        }
    });
}

// 生成二维码
async function generateQRCode(clinicId, clinicName) {
    try {
        const response = await apiRequest(`/clinics/${clinicId}/qrcode`);
        
        currentQRCode = response.qrCode;
        
        document.getElementById('qrcodeDisplay').innerHTML = `
            <img src="${response.qrCode}" alt="诊室二维码">
            <h4>${clinicName}</h4>
        `;
        document.getElementById('qrcodeUrl').textContent = response.url;
        document.getElementById('qrcodeModal').style.display = 'block';
    } catch (error) {
        showMessage('生成二维码失败: ' + error.message, 'error');
    }
}

// 下载二维码
function downloadQRCode() {
    if (currentQRCode) {
        const link = document.createElement('a');
        link.download = 'qrcode.png';
        link.href = currentQRCode;
        link.click();
    }
}

// 加载排队概览
async function loadQueueOverview() {
    try {
        const response = await apiRequest('/admin/queue/overview');
        const queueOverview = document.getElementById('queueOverview');
        
        if (response.length === 0) {
            queueOverview.innerHTML = '<div class="card"><p>暂无排队信息。</p></div>';
            return;
        }
        
        queueOverview.innerHTML = response.map(item => `
            <div class="queue-clinic">
                <h3>
                    ${item.clinic.name}
                    <span class="queue-count">${item.queueCount}人排队</span>
                </h3>
                <div class="queue-list">
                    ${item.queue.length > 0 ? item.queue.map(queue => `
                        <div class="queue-item">
                            <div class="queue-item-info">
                                <span class="queue-number">#${queue.queue_number}</span>
                                <span>${queue.patients.name}</span>
                                <span>${queue.patients.phone}</span>
                            </div>
                            <div class="queue-time-info">
                                <span>${formatDateTime(queue.created_at)}</span>
                                <span class="queue-duration">${formatQueueTime(queue.queue_time_minutes)}</span>
                            </div>
                        </div>
                    `).join('') : '<p>暂无排队</p>'}
                </div>
            </div>
        `).join('');
    } catch (error) {
        showMessage('加载排队概览失败: ' + error.message, 'error');
    }
}

// 刷新排队信息
function refreshQueue() {
    loadQueueOverview();
    showMessage('排队信息已刷新', 'success');
}

// 修改管理员密码
async function handleChangeAdminPassword(event) {
    event.preventDefault();
    
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    showLoading(submitBtn, '修改中...');
    
    try {
        const newPassword = document.getElementById('newAdminPassword').value;
        
        await apiRequest('/admin/password', {
            method: 'PUT',
            body: JSON.stringify({ newPassword })
        });
        
        showMessage('管理员密码修改成功', 'success');
        event.target.reset();
    } catch (error) {
        showMessage('修改密码失败: ' + error.message, 'error');
    } finally {
        hideLoading(submitBtn, originalText);
    }
}

// 修改前台密码
async function handleChangeReceptionPassword(event) {
    event.preventDefault();
    
    const submitBtn = event.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    showLoading(submitBtn, '修改中...');
    
    try {
        const newPassword = document.getElementById('newReceptionPassword').value;
        
        await apiRequest('/admin/reception/password', {
            method: 'PUT',
            body: JSON.stringify({ newPassword })
        });
        
        showMessage('前台密码修改成功', 'success');
        event.target.reset();
    } catch (error) {
        showMessage('修改密码失败: ' + error.message, 'error');
    } finally {
        hideLoading(submitBtn, originalText);
    }
}

// 关闭模态框
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// 点击模态框外部关闭
window.onclick = function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.style.display = 'none';
    }
}
