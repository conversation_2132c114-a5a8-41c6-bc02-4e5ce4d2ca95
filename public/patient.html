<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者排队 - 优眠医学中心</title>
    <link rel="stylesheet" href="styles/common.css">
    <link rel="stylesheet" href="styles/patient.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>优眠医学中心</h1>
            <p>患者排队系统</p>
        </header>

        <main class="main">
            <!-- 排队表单 -->
            <div id="queueForm" class="queue-form">
                <div class="form-header">
                    <h2>排队挂号</h2>
                    <p id="clinicInfo">请填写您的信息进行排队</p>
                </div>

                <form id="patientForm">
                    <div class="form-group">
                        <label for="patientName">姓名</label>
                        <input type="text" id="patientName" name="name" required placeholder="请输入您的姓名">
                    </div>

                    <div class="form-group">
                        <label for="patientPhone">手机号</label>
                        <input type="tel" id="patientPhone" name="phone" required placeholder="请输入您的手机号">
                    </div>

                    <button type="submit" class="btn btn-primary" id="queueBtn">
                        开始排队
                    </button>
                </form>

                <div class="form-footer">
                    <button class="btn btn-secondary" onclick="showQueryForm()">查询排队状态</button>
                </div>
            </div>

            <!-- 查询表单 -->
            <div id="queryForm" class="query-form" style="display: none;">
                <div class="form-header">
                    <h2>查询排队状态</h2>
                    <p>请输入您的手机号查询排队状态</p>
                </div>

                <form id="queryPatientForm">
                    <div class="form-group">
                        <label for="queryPhone">手机号</label>
                        <input type="tel" id="queryPhone" name="phone" required placeholder="请输入您的手机号">
                    </div>

                    <button type="submit" class="btn btn-primary" id="queryBtn">
                        查询状态
                    </button>
                </form>

                <div class="form-footer">
                    <button class="btn btn-secondary" onclick="showQueueForm()">返回排队</button>
                </div>
            </div>

            <!-- 排队结果 -->
            <div id="queueResult" class="queue-result" style="display: none;">
                <div class="result-header">
                    <h2>排队成功</h2>
                    <div class="queue-number" id="queueNumber">#1</div>
                </div>

                <div class="result-info">
                    <div class="info-item">
                        <label>诊室：</label>
                        <span id="resultClinic">-</span>
                    </div>
                    <div class="info-item">
                        <label>姓名：</label>
                        <span id="resultName">-</span>
                    </div>
                    <div class="info-item">
                        <label>手机号：</label>
                        <span id="resultPhone">-</span>
                    </div>
                    <div class="info-item">
                        <label>排队时间：</label>
                        <span id="resultTime">-</span>
                    </div>
                </div>

                <div class="result-actions">
                    <button class="btn btn-primary" onclick="refreshStatus()">刷新状态</button>
                    <button class="btn btn-secondary" onclick="showQueueForm()">重新排队</button>
                </div>
            </div>

            <!-- 查询结果 -->
            <div id="queryResult" class="query-result" style="display: none;">
                <div class="result-header">
                    <h2>排队状态</h2>
                </div>

                <div id="queryResultContent">
                    <!-- 查询结果将在这里显示 -->
                </div>

                <div class="result-actions">
                    <button class="btn btn-primary" onclick="refreshQueryStatus()">刷新状态</button>
                    <button class="btn btn-secondary" onclick="showQueryForm()">重新查询</button>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 优眠医学中心. All rights reserved.</p>
            <p><a href="index.html" style="color: white;">返回首页</a></p>
        </footer>
    </div>

    <script src="scripts/common.js"></script>
    <script>
        let currentClinicId = null;
        let currentPatientPhone = null;

        document.addEventListener('DOMContentLoaded', function() {
            // 获取诊室ID
            currentClinicId = getUrlParameter('clinic');
            
            if (currentClinicId) {
                loadClinicInfo();
            }

            // 绑定表单事件
            document.getElementById('patientForm').addEventListener('submit', handleQueue);
            document.getElementById('queryPatientForm').addEventListener('submit', handleQuery);
        });

        // 加载诊室信息
        async function loadClinicInfo() {
            try {
                const response = await apiRequest('/clinics');
                const clinic = response.find(c => c.id == currentClinicId);
                
                if (clinic) {
                    document.getElementById('clinicInfo').textContent = `${clinic.name} - ${clinic.description || ''}`;
                } else {
                    showMessage('诊室不存在', 'error');
                }
            } catch (error) {
                console.error('加载诊室信息失败:', error);
            }
        }

        // 处理排队
        async function handleQueue(event) {
            event.preventDefault();
            
            const queueBtn = document.getElementById('queueBtn');
            const originalText = queueBtn.textContent;
            showLoading(queueBtn, '排队中...');

            try {
                const formData = new FormData(event.target);
                const queueData = {
                    clinicId: currentClinicId,
                    name: formData.get('name'),
                    phone: formData.get('phone')
                };

                if (!currentClinicId) {
                    throw new Error('请通过二维码扫描进入排队页面');
                }

                const response = await apiRequest('/queue/join', {
                    method: 'POST',
                    body: JSON.stringify(queueData)
                });

                currentPatientPhone = queueData.phone;
                showQueueResult(response.queue);
                showMessage(response.message, 'success');

            } catch (error) {
                showMessage('排队失败: ' + error.message, 'error');
            } finally {
                hideLoading(queueBtn, originalText);
            }
        }

        // 处理查询
        async function handleQuery(event) {
            event.preventDefault();
            
            const queryBtn = document.getElementById('queryBtn');
            const originalText = queryBtn.textContent;
            showLoading(queryBtn, '查询中...');

            try {
                const formData = new FormData(event.target);
                const phone = formData.get('phone');

                const response = await apiRequest(`/queue/status/${phone}`);
                
                currentPatientPhone = phone;
                showQueryResult(response);

            } catch (error) {
                showMessage('查询失败: ' + error.message, 'error');
            } finally {
                hideLoading(queryBtn, originalText);
            }
        }

        // 显示排队结果
        function showQueueResult(queue) {
            document.getElementById('queueForm').style.display = 'none';
            document.getElementById('queryForm').style.display = 'none';
            document.getElementById('queryResult').style.display = 'none';
            document.getElementById('queueResult').style.display = 'block';

            document.getElementById('queueNumber').textContent = `#${queue.queue_number}`;
            document.getElementById('resultClinic').textContent = queue.clinics.name;
            document.getElementById('resultName').textContent = queue.patients.name;
            document.getElementById('resultPhone').textContent = queue.patients.phone;
            document.getElementById('resultTime').textContent = formatDateTime(queue.created_at);
        }

        // 显示查询结果
        function showQueryResult(data) {
            document.getElementById('queueForm').style.display = 'none';
            document.getElementById('queryForm').style.display = 'none';
            document.getElementById('queueResult').style.display = 'none';
            document.getElementById('queryResult').style.display = 'block';

            const content = document.getElementById('queryResultContent');

            if (data.queues.length === 0) {
                content.innerHTML = '<div class="no-queue"><p>您当前没有排队记录</p></div>';
                return;
            }

            content.innerHTML = data.queues.map(queue => `
                <div class="queue-item">
                    <div class="queue-item-header">
                        <span class="queue-number">#${queue.queue_number}</span>
                        <span class="clinic-name">${queue.clinics.name}</span>
                    </div>
                    <div class="queue-item-info">
                        <p><strong>排队状态：</strong>${formatQueuePosition(queue.people_ahead)}</p>
                        <p><strong>排队时长：</strong>${formatQueueTime(queue.queue_time_minutes)}</p>
                        <p><strong>排队时间：</strong>${formatDateTime(queue.created_at)}</p>
                        <p><strong>诊室描述：</strong>${queue.clinics.description || '暂无描述'}</p>
                    </div>
                </div>
            `).join('');
        }

        // 显示排队表单
        function showQueueForm() {
            document.getElementById('queueForm').style.display = 'block';
            document.getElementById('queryForm').style.display = 'none';
            document.getElementById('queueResult').style.display = 'none';
            document.getElementById('queryResult').style.display = 'none';
        }

        // 显示查询表单
        function showQueryForm() {
            document.getElementById('queueForm').style.display = 'none';
            document.getElementById('queryForm').style.display = 'block';
            document.getElementById('queueResult').style.display = 'none';
            document.getElementById('queryResult').style.display = 'none';
        }

        // 刷新状态
        function refreshStatus() {
            if (currentPatientPhone) {
                handleQuery({ preventDefault: () => {}, target: { elements: { phone: { value: currentPatientPhone } } } });
            }
        }

        // 刷新查询状态
        function refreshQueryStatus() {
            if (currentPatientPhone) {
                const form = document.getElementById('queryPatientForm');
                form.elements.phone.value = currentPatientPhone;
                handleQuery({ preventDefault: () => {}, target: form });
            }
        }
    </script>
</body>
</html>
