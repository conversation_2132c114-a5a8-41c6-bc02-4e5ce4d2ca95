/* 管理员页面样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 标签页样式 */
.admin-tabs {
    display: flex;
    gap: 4px;
    margin-bottom: 30px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 4px;
}

.tab-btn {
    flex: 1;
    padding: 12px 24px;
    background: transparent;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.tab-btn:hover {
    background: rgba(255,255,255,0.1);
}

.tab-btn.active {
    background: white;
    color: #333;
}

/* 标签内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 区块头部 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    color: white;
}

.section-header h2 {
    margin: 0;
}

/* 诊室网格 */
.clinics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.clinic-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.clinic-card h3 {
    margin: 0 0 8px 0;
    color: #333;
}

.clinic-card p {
    color: #666;
    margin: 0 0 16px 0;
    font-size: 14px;
}

.clinic-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.clinic-actions .btn {
    flex: 1;
    min-width: 80px;
    padding: 8px 12px;
    font-size: 14px;
}

/* 设置网格 */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.settings-grid .card h3 {
    margin-bottom: 20px;
    color: #333;
}

/* 排队概览 */
.queue-clinic {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.queue-clinic h3 {
    margin: 0 0 16px 0;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.queue-count {
    background: #2196F3;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;
}

.queue-list {
    display: grid;
    gap: 8px;
}

.queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.queue-time-info {
    text-align: right;
    font-size: 12px;
    color: #666;
}

.queue-duration {
    display: block;
    color: #2196F3;
    font-weight: 500;
    margin-top: 2px;
}

.queue-item-info {
    display: flex;
    gap: 16px;
}

.queue-number {
    font-weight: bold;
    color: #2196F3;
    min-width: 40px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #333;
}

.modal form {
    padding: 24px;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
}

/* 二维码容器 */
.qrcode-container {
    text-align: center;
    padding: 24px;
}

.qrcode-container img {
    max-width: 200px;
    margin-bottom: 16px;
}

.qrcode-container p {
    word-break: break-all;
    color: #666;
    font-size: 14px;
    margin-bottom: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .admin-tabs {
        flex-direction: column;
    }
    
    .section-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .clinics-grid {
        grid-template-columns: 1fr;
    }
    
    .clinic-actions {
        flex-direction: column;
    }
    
    .clinic-actions .btn {
        flex: none;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
}
