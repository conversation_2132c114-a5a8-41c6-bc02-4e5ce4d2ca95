/* 前台页面样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 标签页样式 */
.reception-tabs {
    display: flex;
    gap: 4px;
    margin-bottom: 30px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 4px;
}

.tab-btn {
    flex: 1;
    padding: 12px 24px;
    background: transparent;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.tab-btn:hover {
    background: rgba(255,255,255,0.1);
}

.tab-btn.active {
    background: white;
    color: #333;
}

/* 标签内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 区块头部 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    color: white;
}

.section-header h2 {
    margin: 0;
}

/* 概览统计 */
.overview-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 16px;
    min-width: 120px;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: white;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
    margin-top: 4px;
}

/* 诊室概览 */
.clinics-overview {
    display: grid;
    gap: 20px;
}

.clinic-overview-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.clinic-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.clinic-header h3 {
    margin: 0;
    color: #333;
}

.queue-count {
    background: #6c757d;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;
}

.queue-count.has-queue {
    background: #2196F3;
}

.clinic-description {
    margin-bottom: 16px;
}

.clinic-description p {
    color: #666;
    margin: 0;
    font-size: 14px;
}

/* 排队列表 */
.queue-list {
    display: grid;
    gap: 8px;
}

.queue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    position: relative;
}

.queue-item.current {
    background: #e3f2fd;
    border-left: 4px solid #2196F3;
}

.queue-item-info {
    display: flex;
    gap: 16px;
    align-items: center;
}

.queue-number {
    font-weight: bold;
    color: #2196F3;
    min-width: 40px;
}

.patient-name {
    font-weight: 500;
    color: #333;
}

.patient-phone {
    color: #666;
    font-size: 14px;
}

.queue-time {
    color: #666;
    font-size: 12px;
    text-align: right;
}

.queue-duration {
    color: #2196F3;
    font-weight: 500;
    font-size: 11px;
}

.current-label {
    position: absolute;
    top: -8px;
    right: 12px;
    background: #2196F3;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.no-queue {
    text-align: center;
    color: #666;
    padding: 20px;
    font-style: italic;
}

/* 搜索表单 */
.search-form {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.search-form form {
    display: flex;
    gap: 16px;
    align-items: end;
}

.search-form .form-group {
    flex: 1;
    margin-bottom: 0;
}

.search-form .btn {
    white-space: nowrap;
}

/* 搜索结果 */
.search-result-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.search-result-card h3,
.search-result-card h4 {
    color: #333;
    margin-bottom: 16px;
}

.patient-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.patient-info p {
    margin: 8px 0;
    color: #333;
}

.queue-records {
    display: grid;
    gap: 12px;
}

.queue-record {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 16px;
}

.queue-record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.queue-record-header .queue-number {
    font-size: 1.1rem;
}

.clinic-name {
    font-weight: 500;
    color: #333;
}

.queue-status {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.queue-record-info p {
    margin: 4px 0;
    color: #666;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .reception-tabs {
        flex-direction: column;
    }
    
    .section-header {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
    
    .overview-stats {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .search-form form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .queue-item-info {
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
    }
    
    .queue-record-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
